// Mock data for Discuss module
import type {
  User,
  Message,
  Channel,
  DirectMessage,
  Team,
  NotificationSettings,
  PresenceInfo,
} from '../../modules/discuss/types';

// Mock users
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'JD',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'JS',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'MW',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'SJ',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
  },
  {
    id: '5',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'DB',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
  },
];

// Mock channels
export const mockChannels: Channel[] = [
  {
    id: 'general',
    name: 'general',
    description: 'General discussion and announcements for the team',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastActivity: new Date(),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'development',
    name: 'development',
    description: 'Development discussions and code reviews',
    type: 'public',
    memberIds: ['1', '3', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-02'),
    lastActivity: new Date(Date.now() - 30 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'design',
    name: 'design',
    description: 'Design discussions and feedback',
    type: 'public',
    memberIds: ['2', '4'],
    createdBy: '2',
    createdAt: new Date('2024-01-03'),
    lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'marketing',
    name: 'marketing',
    description: 'Marketing campaigns and strategies',
    type: 'private',
    memberIds: ['2', '4', '5'],
    createdBy: '2',
    createdAt: new Date('2024-01-04'),
    lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: false,
    },
  },
  {
    id: 'random',
    name: 'random',
    description: 'Random conversations and fun stuff',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '3',
    createdAt: new Date('2024-01-05'),
    lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: false,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
];

// Mock messages
export const mockMessages: Message[] = [
  {
    id: 'msg-1',
    content: 'Welcome everyone to our new discuss platform! 🎉 Feel free to share ideas, ask questions, and collaborate here.',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T09:30:00'),
    reactions: [
      { emoji: '👍', userIds: ['2', '3', '4'], count: 3 },
      { emoji: '🎉', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-2',
    content: 'This looks great! I love the clean interface. Can we also add file sharing capabilities?',
    authorId: '2',
    channelId: 'general',
    timestamp: new Date('2024-01-10T10:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['1'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-3',
    content: '@Jane Smith Yes! File sharing is definitely on the roadmap. We\'re also planning to add video calls and screen sharing.',
    authorId: '3',
    channelId: 'general',
    timestamp: new Date('2024-01-10T11:00:00'),
    reactions: [],
    attachments: [],
    mentions: ['2'],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-4',
    content: 'Hey! I wanted to discuss the new project requirements with you. Do you have some time today?',
    authorId: '1',
    timestamp: new Date('2024-01-09T15:45:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-5',
    content: 'Sure! I\'m free after 5 PM. Should we schedule a quick call?',
    authorId: '2',
    timestamp: new Date('2024-01-09T16:12:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
];

// Mock direct messages
export const mockDirectMessages: DirectMessage[] = [
  {
    id: 'dm-1-2',
    participantIds: ['1', '2'],
    lastMessage: mockMessages.find(m => m.id === 'msg-5'),
    lastActivity: new Date('2024-01-09T16:12:00'),
    isArchived: false,
  },
  {
    id: 'dm-1-3',
    participantIds: ['1', '3'],
    lastActivity: new Date('2024-01-08T14:30:00'),
    isArchived: false,
  },
];

// Mock teams
export const mockTeams: Team[] = [
  {
    id: 'frontend-team',
    name: 'Frontend Team',
    description: 'UI/UX development and design implementation',
    memberIds: ['1', '2', '4'],
    channelIds: ['general', 'design'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    settings: {
      visibility: 'public',
      joinPolicy: 'open',
      allowMemberInvites: true,
    },
  },
  {
    id: 'backend-team',
    name: 'Backend Team',
    description: 'Server-side development and API management',
    memberIds: ['3', '5'],
    channelIds: ['development'],
    createdBy: '3',
    createdAt: new Date('2024-01-02'),
    settings: {
      visibility: 'public',
      joinPolicy: 'invite-only',
      allowMemberInvites: false,
    },
  },
];

// Mock notification settings
export const mockNotificationSettings: NotificationSettings = {
  desktop: true,
  sound: true,
  email: false,
  mobile: true,
  mentions: true,
  directMessages: true,
  channels: true,
  doNotDisturbStart: '22:00',
  doNotDisturbEnd: '08:00',
};

// Mock presence info
export const mockPresenceInfo: PresenceInfo[] = [
  {
    userId: '1',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '2',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000),
    isTyping: true,
    currentChannel: 'general',
  },
  {
    userId: '3',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '4',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isTyping: false,
  },
  {
    userId: '5',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000),
    isTyping: false,
  },
];

// Helper functions to get mock data
export const getMockUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getMockChannelById = (id: string): Channel | undefined => {
  return mockChannels.find(channel => channel.id === id);
};

export const getMockMessagesByChannelId = (channelId: string): Message[] => {
  return mockMessages.filter(message => message.channelId === channelId);
};

export const getMockDirectMessagesByUserId = (userId: string): DirectMessage[] => {
  return mockDirectMessages.filter(dm => dm.participantIds.includes(userId));
};

export const getMockTeamById = (id: string): Team | undefined => {
  return mockTeams.find(team => team.id === id);
};
