// MSW handlers for Discuss module API endpoints
import { http, HttpResponse } from 'msw';
import {
  mockUsers,
  mockChannels,
  mockMessages,
  mockDirectMessages,
  mockTeams,
  mockNotificationSettings,
  mockPresenceInfo,
  getMockUserById,
  getMockChannelById,
  getMockMessagesByChannelId,
  getMockDirectMessagesByUserId,
  getMockTeamById,
} from '../data/discuss';

// Base API URL for discuss endpoints
const API_BASE = '/api/discuss';

export const discussHandlers = [
  // Get all users
  http.get(`${API_BASE}/users`, () => {
    return HttpResponse.json({
      success: true,
      data: mockUsers,
    });
  }),

  // Get user by ID
  http.get(`${API_BASE}/users/:id`, ({ params }) => {
    const { id } = params;
    const user = getMockUserById(id as string);
    
    if (!user) {
      return HttpResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: user,
    });
  }),

  // Get all channels
  http.get(`${API_BASE}/channels`, () => {
    return HttpResponse.json({
      success: true,
      data: mockChannels,
    });
  }),

  // Get channel by ID
  http.get(`${API_BASE}/channels/:id`, ({ params }) => {
    const { id } = params;
    const channel = getMockChannelById(id as string);
    
    if (!channel) {
      return HttpResponse.json(
        { success: false, error: 'Channel not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: channel,
    });
  }),

  // Get messages for a channel
  http.get(`${API_BASE}/channels/:id/messages`, ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '50');
    
    const messages = getMockMessagesByChannelId(id as string);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedMessages = messages.slice(startIndex, endIndex);

    return HttpResponse.json({
      success: true,
      data: paginatedMessages,
      total: messages.length,
      page,
      pageSize,
      hasMore: endIndex < messages.length,
    });
  }),

  // Send a message
  http.post(`${API_BASE}/messages`, async ({ request }) => {
    const body = await request.json() as any;
    
    const newMessage = {
      id: `msg-${Date.now()}`,
      content: body.content,
      authorId: body.authorId || '1', // Default to current user
      channelId: body.channelId,
      timestamp: new Date(),
      reactions: [],
      attachments: body.attachments || [],
      mentions: body.mentions || [],
      isDeleted: false,
      deliveryStatus: 'sent' as const,
    };

    // Add to mock data (in real app, this would be persisted)
    mockMessages.push(newMessage);

    return HttpResponse.json({
      success: true,
      data: newMessage,
    });
  }),

  // Get direct messages for current user
  http.get(`${API_BASE}/direct-messages`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId') || '1'; // Default to current user
    
    const directMessages = getMockDirectMessagesByUserId(userId);

    return HttpResponse.json({
      success: true,
      data: directMessages,
    });
  }),

  // Get all teams
  http.get(`${API_BASE}/teams`, () => {
    return HttpResponse.json({
      success: true,
      data: mockTeams,
    });
  }),

  // Get team by ID
  http.get(`${API_BASE}/teams/:id`, ({ params }) => {
    const { id } = params;
    const team = getMockTeamById(id as string);
    
    if (!team) {
      return HttpResponse.json(
        { success: false, error: 'Team not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: team,
    });
  }),

  // Get notification settings
  http.get(`${API_BASE}/settings/notifications`, () => {
    return HttpResponse.json({
      success: true,
      data: mockNotificationSettings,
    });
  }),

  // Update notification settings
  http.put(`${API_BASE}/settings/notifications`, async ({ request }) => {
    const body = await request.json() as any;
    
    // In real app, this would update the user's settings
    Object.assign(mockNotificationSettings, body);

    return HttpResponse.json({
      success: true,
      data: mockNotificationSettings,
    });
  }),

  // Get presence info for all users
  http.get(`${API_BASE}/presence`, () => {
    return HttpResponse.json({
      success: true,
      data: mockPresenceInfo,
    });
  }),

  // Update user presence
  http.put(`${API_BASE}/presence`, async ({ request }) => {
    const body = await request.json() as any;
    
    const existingPresence = mockPresenceInfo.find(p => p.userId === body.userId);
    if (existingPresence) {
      Object.assign(existingPresence, body);
    } else {
      mockPresenceInfo.push(body);
    }

    return HttpResponse.json({
      success: true,
      data: body,
    });
  }),

  // Search messages
  http.get(`${API_BASE}/search`, ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const channelId = url.searchParams.get('channelId');
    
    let filteredMessages = mockMessages;
    
    if (channelId) {
      filteredMessages = filteredMessages.filter(m => m.channelId === channelId);
    }
    
    if (query) {
      filteredMessages = filteredMessages.filter(m => 
        m.content.toLowerCase().includes(query.toLowerCase())
      );
    }

    const results = filteredMessages.map(message => ({
      message,
      channel: getMockChannelById(message.channelId || ''),
      author: getMockUserById(message.authorId),
      highlights: [query], // Simple highlighting
    }));

    return HttpResponse.json({
      success: true,
      data: results,
    });
  }),

  // File upload endpoint
  http.post(`${API_BASE}/files/upload`, async ({ request }) => {
    // Simulate file upload
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockFile = {
      id: `file-${Date.now()}`,
      name: 'uploaded-file.jpg',
      type: 'image',
      url: '/mock-files/uploaded-file.jpg',
      size: 1024 * 1024, // 1MB
      mimeType: 'image/jpeg',
    };

    return HttpResponse.json({
      success: true,
      data: mockFile,
    });
  }),

  // WebSocket connection simulation (for real-time features)
  http.get(`${API_BASE}/ws`, () => {
    // In a real app, this would establish a WebSocket connection
    return HttpResponse.json({
      success: true,
      message: 'WebSocket connection established',
    });
  }),

  // Typing indicator
  http.post(`${API_BASE}/typing`, async ({ request }) => {
    const body = await request.json() as any;
    
    // Update typing status
    const presence = mockPresenceInfo.find(p => p.userId === body.userId);
    if (presence) {
      presence.isTyping = body.isTyping;
      presence.currentChannel = body.channelId;
    }

    return HttpResponse.json({
      success: true,
      data: { userId: body.userId, isTyping: body.isTyping },
    });
  }),

  // Message reactions
  http.post(`${API_BASE}/messages/:messageId/reactions`, async ({ params, request }) => {
    const { messageId } = params;
    const body = await request.json() as any;
    
    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    const existingReaction = message.reactions.find(r => r.emoji === body.emoji);
    if (existingReaction) {
      if (!existingReaction.userIds.includes(body.userId)) {
        existingReaction.userIds.push(body.userId);
        existingReaction.count++;
      }
    } else {
      message.reactions.push({
        emoji: body.emoji,
        userIds: [body.userId],
        count: 1,
      });
    }

    return HttpResponse.json({
      success: true,
      data: message.reactions,
    });
  }),

  // Remove message reaction
  http.delete(`${API_BASE}/messages/:messageId/reactions/:emoji`, ({ params, request }) => {
    const { messageId, emoji } = params;
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId') || '1';
    
    const message = mockMessages.find(m => m.id === messageId);
    if (!message) {
      return HttpResponse.json(
        { success: false, error: 'Message not found' },
        { status: 404 }
      );
    }

    const reaction = message.reactions.find(r => r.emoji === emoji);
    if (reaction) {
      reaction.userIds = reaction.userIds.filter(id => id !== userId);
      reaction.count = reaction.userIds.length;
      
      if (reaction.count === 0) {
        message.reactions = message.reactions.filter(r => r.emoji !== emoji);
      }
    }

    return HttpResponse.json({
      success: true,
      data: message.reactions,
    });
  }),
];
