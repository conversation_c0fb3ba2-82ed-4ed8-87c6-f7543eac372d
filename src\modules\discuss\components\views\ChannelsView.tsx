import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { MessageList, MessageInput } from '../core';
import { mockMessages, mockUsers, getMockMessagesByChannelId, getMockUserById } from '../../../../mocks/data/discuss';
import type { Message, User } from '../../types';

export interface ChannelsViewProps {
  className?: string;
  'data-testid'?: string;
}

export const ChannelsView: React.FC<ChannelsViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [currentChannelId] = useState('general'); // Default to general channel
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const currentUserId = '1'; // Mock current user

  // Load messages for current channel
  useEffect(() => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      const channelMessages = getMockMessagesByChannelId(currentChannelId);
      setMessages(channelMessages);
      setIsLoading(false);
    }, 500);
  }, [currentChannelId]);

  const handleSendMessage = (content: string, attachments?: File[]) => {
    const newMessage: Message = {
      id: `msg-${Date.now()}`,
      content,
      authorId: currentUserId,
      channelId: currentChannelId,
      timestamp: new Date(),
      reactions: [],
      attachments: attachments?.map(file => ({
        id: `att-${Date.now()}`,
        name: file.name,
        type: file.type.startsWith('image/') ? 'image' :
              file.type.startsWith('video/') ? 'video' :
              file.type.startsWith('audio/') ? 'audio' : 'document',
        url: URL.createObjectURL(file),
        size: file.size,
        mimeType: file.type,
      })) || [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'sent',
    };

    setMessages(prev => [...prev, newMessage]);
  };

  const handleReaction = (messageId: string, emoji: string) => {
    setMessages(prev => prev.map(message => {
      if (message.id === messageId) {
        const existingReaction = message.reactions.find(r => r.emoji === emoji);
        if (existingReaction) {
          if (existingReaction.userIds.includes(currentUserId)) {
            // Remove reaction
            existingReaction.userIds = existingReaction.userIds.filter(id => id !== currentUserId);
            existingReaction.count--;
            if (existingReaction.count === 0) {
              message.reactions = message.reactions.filter(r => r.emoji !== emoji);
            }
          } else {
            // Add reaction
            existingReaction.userIds.push(currentUserId);
            existingReaction.count++;
          }
        } else {
          // New reaction
          message.reactions.push({
            emoji,
            userIds: [currentUserId],
            count: 1,
          });
        }
      }
      return message;
    }));
  };

  const handleTyping = (isTyping: boolean) => {
    // TODO: Implement typing indicator
    console.log('Typing:', isTyping);
  };

  return (
    <div
      className={`flex-1 flex flex-col ${className}`}
      data-testid={testId}
    >
      {/* Channel Header */}
      <div
        className="px-6 py-4 border-b"
        style={{
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h1 className="text-xl font-semibold" style={{ color: colors.text }}>
              📢 #general
            </h1>
            <span
              className="text-sm px-2 py-1 rounded-full"
              style={{
                backgroundColor: colors.backgroundSecondary,
                color: colors.textSecondary,
              }}
            >
              42 members
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="Channel info"
            >
              ℹ️
            </button>
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="Start call"
            >
              📞
            </button>
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="More options"
            >
              ⋯
            </button>
          </div>
        </div>
        <p className="text-sm mt-1" style={{ color: colors.textSecondary }}>
          General discussion and announcements for the team
        </p>
      </div>

      {/* Messages Area */}
      <MessageList
        messages={messages}
        users={mockUsers}
        currentUserId={currentUserId}
        channelId={currentChannelId}
        isLoading={isLoading}
        onReaction={handleReaction}
        onReply={(messageId) => console.log('Reply to:', messageId)}
        onEdit={(messageId) => console.log('Edit:', messageId)}
        onDelete={(messageId) => console.log('Delete:', messageId)}
        onPin={(messageId) => console.log('Pin:', messageId)}
      />

      {/* Message Input */}
      <MessageInput
        placeholder="Type a message in #general..."
        channelId={currentChannelId}
        onSendMessage={handleSendMessage}
        onTyping={handleTyping}
      />
    </div>
  );
};
